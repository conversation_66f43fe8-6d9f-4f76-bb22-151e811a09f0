import React, { useState, useEffect } from "react";
// import { Sun, Moon } from "lucide-react";
// import { Sidebar } from "./components/Sidebar";
// import { ResultCard } from "./components/ResultCard";
import { MatchData } from "./types";
// import {
//   calculateCutoffTimes,
//   calculateDelayedStart,
//   calculateInterruption1st,
//   calculateInterruption2nd,
// } from "./calculations";
import { timeToMins, minsToTime } from "./utils";

const initialData: MatchData = {
  teams: "Team A vs Team B",
  startTime: "15:00",
  cutoffScenario: "game",

  // Delayed Start
  ds_A: 170,
  ds_C: 0,
  ds_D: 30,
  ds_E: 0,
  ds_interval_minutes: 10,

  // Interruption 1st Innings
  t20_scenario: "interruption",
  i1_AA: 170,
  i1_BB: 0,
  i1_CC: 0,
  i1_DD: 30,
  i1_EE: 0,

  // Terminate scenario
  term_P: "17:00",
  term_Q: "18:00",
  term_R: 60,
  term_S: 14,
  term_T: 0,

  // Interruption 2nd Innings
  i2_starttime: "16:45",
  i2_length: 90,
  i2_CCC: 0,
  i2_DDD: "17:15",
  i2_EEE: 0,
  i2_FFF: 0,
  i2_HHH: 20,
};

function App() {
  const [data, setData] = useState<MatchData>(initialData);
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Initialize dark mode from localStorage or system preference
  useEffect(() => {
    const savedTheme = localStorage.getItem("color-theme");
    const prefersDark = window.matchMedia(
      "(prefers-color-scheme: dark)"
    ).matches;

    if (savedTheme === "dark" || (!savedTheme && prefersDark)) {
      setIsDarkMode(true);
      document.documentElement.classList.add("dark");
    } else {
      setIsDarkMode(false);
      document.documentElement.classList.remove("dark");
    }
  }, []);

  // Toggle dark mode
  const toggleDarkMode = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);

    if (newDarkMode) {
      document.documentElement.classList.add("dark");
      localStorage.setItem("color-theme", "dark");
    } else {
      document.documentElement.classList.remove("dark");
      localStorage.setItem("color-theme", "light");
    }
  };

  // Update calculated fields when data changes
  useEffect(() => {
    const updatedData = { ...data };

    // Calculate ds_E
    if (data.ds_C > data.ds_D) {
      updatedData.ds_E = Math.min(10, data.ds_C - data.ds_D);
    } else {
      updatedData.ds_E = 0;
    }

    // Calculate i1_EE
    if (data.i1_CC > data.i1_DD) {
      updatedData.i1_EE = Math.min(10, data.i1_CC - data.i1_DD);
    } else {
      updatedData.i1_EE = 0;
    }

    // Calculate terminate scenario fields
    const p_mins = timeToMins(data.term_P);
    const q_mins = timeToMins(data.term_Q);
    updatedData.term_R = q_mins - p_mins;
    updatedData.term_S = Math.ceil(updatedData.term_R / 4.25);

    if (JSON.stringify(updatedData) !== JSON.stringify(data)) {
      setData(updatedData);
    }
  }, [data.ds_C, data.ds_D, data.i1_CC, data.i1_DD, data.term_P, data.term_Q]);

  // Calculate scheduled hours of play
  const startTimeMins = timeToMins(data.startTime);
  const firstInnsEnd = minsToTime(startTimeMins + 90);
  const secondInnsStart = minsToTime(startTimeMins + 90 + 20);
  const secondInnsEnd = minsToTime(startTimeMins + 90 + 20 + 90);

  // Calculate results
  // const cutoffResult = calculateCutoffTimes(data);
  // const delayedStartResult = calculateDelayedStart(data);
  // const interruption1stResult = calculateInterruption1st(data);
  // const interruption2ndResult = calculateInterruption2nd(data);

  return (
    <div className="min-h-screen bg-blue-500 text-white p-8">
      <h1 className="text-4xl font-bold mb-4">T20 Cricket Calculator</h1>
      <p className="text-xl mb-4">Testing step by step...</p>

      <div className="bg-white text-blue-500 p-4 rounded mb-4">
        <p>Start Time: {data.startTime}</p>
        <p>Teams: {data.teams}</p>
        <p>First Innings End: {firstInnsEnd}</p>
        <p>Second Innings Start: {secondInnsStart}</p>
      </div>

      <div className="bg-yellow-400 text-black p-4 rounded">
        <p>If you can see this, basic React + Tailwind + Types are working!</p>
        <p>Current time: {new Date().toLocaleTimeString()}</p>
      </div>
    </div>
  );
}

export default App;
